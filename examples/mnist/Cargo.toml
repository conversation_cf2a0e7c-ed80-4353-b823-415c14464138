[package]
authors = ["nathanielsimard <<EMAIL>>"]
edition.workspace = true
license.workspace = true
name = "mnist"
publish = false
version.workspace = true

[lints]
workspace = true

[features]
default = ["burn/dataset", "burn/vision"]
ndarray = ["burn/ndarray"]
ndarray-blas-accelerate = ["burn/ndarray", "burn/accelerate"]
ndarray-blas-netlib = ["burn/ndarray", "burn/blas-netlib"]
ndarray-blas-openblas = ["burn/ndarray", "burn/openblas"]
tch-cpu = ["burn/tch"]
tch-gpu = ["burn/tch"]
wgpu = ["burn/wgpu"]
metal = ["burn/metal"]
remote = ["burn/remote"]

[dependencies]
burn = { path = "../../crates/burn", features = ["train", "tch-gpu"] }

# Serialization
log = { workspace = true }
serde = { workspace = true, features = ["std", "derive"] }
